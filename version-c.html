<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>From Zero To $100/Day Online - FREE Blueprint</title>
    <meta name="description" content="Get your FREE Make Money Online Ebook! Proven strategies to start earning online today.">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Analytics (replace GA_MEASUREMENT_ID with your actual ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>

    <style>
        .glow-button {
            box-shadow: 0 0 20px rgba(168, 85, 247, 0.4);
            transition: all 0.3s ease;
        }
        .glow-button:hover {
            box-shadow: 0 0 30px rgba(168, 85, 247, 0.6);
            transform: translateY(-2px);
        }
        .gradient-text {
            background: linear-gradient(45deg, #f59e0b, #ef4444);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12 max-w-2xl">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <div class="mb-8">
                <div class="text-6xl mb-6">📈</div>
                <h1 class="text-4xl md:text-5xl font-bold mb-6">
                    From Zero To <span class="gradient-text">$100/Day</span> Online
                </h1>
                <p class="text-xl text-gray-300 mb-8">
                    The complete blueprint to build a profitable online income stream
                </p>
            </div>
        </div>

        <!-- Simple Benefits -->
        <div class="text-center mb-12">
            <div class="space-y-4 text-gray-300">
                <p>� Beginner-friendly methods that actually work</p>
                <p>� Real case studies from successful students</p>
                <p>� Scale to $100+ per day within 30 days</p>
            </div>
        </div>

        <!-- Lead Capture Form -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 border border-purple-500/30 rounded-lg p-8 max-w-md mx-auto">
            <h3 class="text-2xl font-bold text-center mb-6">
                Get Your FREE Blueprint!
            </h3>

            <form id="leadForm" class="space-y-6">
                <div>
                    <input type="text" id="firstName" name="firstName" required
                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                           placeholder="Your first name">
                </div>

                <div>
                    <input type="email" id="email" name="email" required
                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                           placeholder="Your email address">
                </div>

                <button type="submit" id="downloadBtn"
                        class="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-4 px-6 rounded-lg glow-button">
                    � DOWNLOAD BLUEPRINT NOW 🚀
                </button>
            </form>

            <p class="text-xs text-gray-400 text-center mt-4">
                ⚠️ <strong class="text-red-400">Limited Time:</strong> Only 147 downloads remaining today!
            </p>
        </div>

        <!-- Enhanced Social Proof -->
        <div class="text-center mt-8">
            <div class="space-y-4">
                <div>
                    <div class="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
                    <p class="text-gray-400 text-sm">"Hit $100/day in just 2 weeks using this blueprint!" - Jessica R.</p>
                </div>
                <div>
                    <div class="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
                    <p class="text-gray-400 text-sm">"Started from zero, now making $150/day consistently!" - Alex M.</p>
                </div>
                <div>
                    <div class="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
                    <p class="text-gray-400 text-sm">"Clear instructions, real results. $89/day and growing!" - Rachel S.</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Simple Footer -->
    <footer class="text-center py-6 text-gray-500 text-sm">
        <p>&copy; 2024 Make Money Online. All rights reserved.</p>
    </footer>

    <!-- JavaScript for Form Handling -->
    <script>
        // Form submission handler
        document.getElementById('leadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const firstName = document.getElementById('firstName').value;
            const email = document.getElementById('email').value;
            
            if (!firstName || !email) {
                alert('Please fill in all fields.');
                return;
            }
            
            // Track conversion
            if (typeof gtag !== 'undefined') {
                gtag('event', 'conversion', {
                    'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL',
                    'value': 1.0,
                    'currency': 'USD',
                    'custom_parameter': 'version_c_file_locker'
                });
            }
            
            // Store lead data
            const leadData = {
                firstName: firstName,
                email: email,
                version: 'C',
                lockerType: 'file',
                timestamp: new Date().toISOString()
            };
            
            console.log('Lead captured (Version C):', leadData);
            
            // Redirect to file locker
            redirectToFileLocker(leadData);
        });

        function redirectToFileLocker(leadData) {
            // Replace this URL with your actual file locker URL
            const fileLockerUrl = 'https://your-affiliate-network.com/file-locker/ebook-package';
            
            // You can append lead data to the URL if needed
            const urlWithData = `${fileLockerUrl}?email=${encodeURIComponent(leadData.email)}&name=${encodeURIComponent(leadData.firstName)}`;
            
            // Redirect to file locker
            window.location.href = urlWithData;
        }
    </script>
</body>
</html>

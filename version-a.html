<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Want Your First $1000 Online? Here's the Exact Blueprint</title>
    <meta name="description" content="Get your FREE Make Money Online Ebook! Proven strategies to start earning online today.">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Analytics (replace GA_MEASUREMENT_ID with your actual ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>

    <style>
        .glow-button {
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.4);
            transition: all 0.3s ease;
        }
        .glow-button:hover {
            box-shadow: 0 0 30px rgba(34, 197, 94, 0.6);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen">
    <!-- Main Content -->
    <main class="container mx-auto px-4 py-12 max-w-2xl">
        <!-- Hero Section -->
        <div class="text-center mb-12">
            <div class="mb-8">
                <div class="text-6xl mb-6">💰</div>
                <h1 class="text-4xl md:text-5xl font-bold mb-6">
                    Want Your First <span class="text-green-400">$1000</span> Online? Here's the Exact Blueprint.
                </h1>
                <p class="text-xl text-gray-300 mb-8">
                    The step-by-step system that's already working for thousands
                </p>
            </div>
        </div>

        <!-- Emotional Benefits -->
        <div class="text-center mb-12">
            <div class="space-y-4 text-gray-300">
                <p>🔥 <strong>Finally escape the 9-5 grind</strong> - Start earning while you sleep</p>
                <p>⚡ <strong>No more money stress</strong> - Generate income in the next 24 hours</p>
                <p>💎 <strong>The exact blueprint</strong> - Copy what's already working for others</p>
            </div>
        </div>

        <!-- Lead Capture Form -->
        <div class="bg-gray-800 border border-gray-700 rounded-lg p-8 max-w-md mx-auto">
            <h3 class="text-2xl font-bold text-center mb-6">
                Download Your FREE Ebook Now!
            </h3>

            <form id="leadForm" class="space-y-6">
                <div>
                    <input type="text" id="firstName" name="firstName" required
                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Your first name">
                </div>

                <div>
                    <input type="email" id="email" name="email" required
                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:border-transparent"
                           placeholder="Your email address">
                </div>

                <button type="submit" id="downloadBtn"
                        class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-6 rounded-lg glow-button">
                    � GET MY FREE EBOOK NOW
                </button>
            </form>

            <p class="text-xs text-gray-400 text-center mt-4">
                ⚠️ <strong class="text-red-400">Limited Time:</strong> Only 89 downloads remaining today!
            </p>
        </div>

        <!-- Professional Reviews Section -->
        <div class="mt-12">
            <h4 class="text-xl font-bold text-center mb-8 text-gray-200">What Our Students Are Saying:</h4>
            <div class="grid gap-6 md:grid-cols-1 max-w-2xl mx-auto">
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-center mb-3">
                        <div class="text-yellow-400 text-lg">⭐⭐⭐⭐⭐</div>
                        <span class="ml-3 font-semibold text-green-400">Sarah M.</span>
                    </div>
                    <p class="text-gray-300 italic">"Made <span class="text-green-400 font-bold">$847 in my first month</span> following this blueprint! The step-by-step instructions made it so easy to understand."</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-center mb-3">
                        <div class="text-yellow-400 text-lg">⭐⭐⭐⭐⭐</div>
                        <span class="ml-3 font-semibold text-green-400">Marcus R.</span>
                    </div>
                    <p class="text-gray-300 italic">"Finally quit my job thanks to this system. <span class="text-green-400 font-bold">Best decision ever!</span> Now I work from home and make more than my old salary."</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 rounded-lg p-6">
                    <div class="flex items-center mb-3">
                        <div class="text-yellow-400 text-lg">⭐⭐⭐⭐⭐</div>
                        <span class="ml-3 font-semibold text-green-400">Jennifer K.</span>
                    </div>
                    <p class="text-gray-300 italic">"Simple to follow, actually works. Hit <span class="text-green-400 font-bold">$1200 last week!</span> Can't believe I waited so long to start."</p>
                </div>
            </div>
        </div>
    </main>

    <!-- Simple Footer -->
    <footer class="text-center py-6 text-gray-500 text-sm">
        <p>&copy; 2024 Make Money Online. All rights reserved.</p>
    </footer>

    <!-- JavaScript for URL Parameter Detection and Form Handling -->
    <script>
        // Get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Detect locker type from URL parameter
        const lockerType = getUrlParameter('locker') || 'file'; // Default to file locker
        
        // Track which variant is being shown
        console.log('Locker variant:', lockerType);
        
        // Send tracking event to Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                'custom_parameter': 'locker_type',
                'value': lockerType
            });
        }

        // Form submission handler
        document.getElementById('leadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const firstName = document.getElementById('firstName').value;
            const email = document.getElementById('email').value;
            
            if (!firstName || !email) {
                alert('Please fill in all fields.');
                return;
            }
            
            // Track conversion
            if (typeof gtag !== 'undefined') {
                gtag('event', 'conversion', {
                    'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL', // Replace with your conversion tracking
                    'value': 1.0,
                    'currency': 'USD',
                    'custom_parameter': lockerType
                });
            }
            
            // Store lead data (you can send this to your backend/email service)
            const leadData = {
                firstName: firstName,
                email: email,
                lockerType: lockerType,
                timestamp: new Date().toISOString(),
                utmSource: getUrlParameter('utm_source'),
                utmMedium: getUrlParameter('utm_medium'),
                utmCampaign: getUrlParameter('utm_campaign')
            };
            
            console.log('Lead captured:', leadData);
            
            // Redirect to file locker (Version A)
            redirectToFileLocker(leadData);
        });

        function redirectToFileLocker(leadData) {
            // Replace this URL with your actual file locker URL
            const fileLockerUrl = 'https://your-affiliate-network.com/file-locker/ebook-package';

            // You can append lead data to the URL if needed
            const urlWithData = `${fileLockerUrl}?email=${encodeURIComponent(leadData.email)}&name=${encodeURIComponent(leadData.firstName)}`;

            // Redirect to file locker
            window.location.href = urlWithData;
        }
    </script>
</body>
</html>

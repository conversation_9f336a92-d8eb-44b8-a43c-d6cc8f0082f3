# Google Ads Split Testing Setup Guide

## 🎯 Step 1: Set Up Conversion Tracking

### In Google Ads:
1. Go to **Tools & Settings** → **Conversions**
2. Click **+ New Conversion Action**
3. Choose **Website**
4. Enter your website URL
5. Set up conversion details:
   - **Category:** Lead
   - **Value:** Use the same value for each conversion
   - **Count:** One
   - **Attribution model:** Data-driven

6. Copy your **Conversion ID** and **Conversion Label**
7. Update these in your `config.js` file

### In Your Squeeze Page:
Replace these placeholders in `config.js`:
```javascript
googleAdsConversionId: 'AW-123456789', // Your actual conversion ID
googleAdsConversionLabel: 'abcd1234_efgh5678', // Your actual conversion label
```

## 📊 Step 2: Create Split Test Campaigns

### Campaign A: File Locker Test
1. **Campaign Name:** "Ebook - File Locker Test"
2. **Final URL:** `https://yourdomain.com/?locker=file&utm_source=google&utm_medium=cpc&utm_campaign=ebook-file`
3. **UTM Parameters:**
   - utm_source=google
   - utm_medium=cpc
   - utm_campaign=ebook-file

### Campaign B: Captcha Locker Test
1. **Campaign Name:** "Ebook - Captcha Locker Test"
2. **Final URL:** `https://yourdomain.com/?locker=captcha&utm_source=google&utm_medium=cpc&utm_campaign=ebook-captcha`
3. **UTM Parameters:**
   - utm_source=google
   - utm_medium=cpc
   - utm_campaign=ebook-captcha

## 🔧 Step 3: Configure Campaign Settings

### Budget & Bidding:
- **Budget:** Split your total budget equally between campaigns
- **Bidding:** Use "Maximize conversions" or "Target CPA"
- **Start with equal budgets** to get fair comparison data

### Targeting:
- **Use identical targeting** for both campaigns:
  - Same keywords
  - Same demographics
  - Same locations
  - Same devices
  - Same ad schedule

### Ad Copy:
- **Use identical ad copy** for fair testing
- Focus on the ebook benefit, not the locker type
- Example headlines:
  - "Free Make Money Online Ebook"
  - "Download Your Free Money-Making Guide"
  - "Get Rich Online - Free Ebook Package"

## 📈 Step 4: Monitor Results

### Key Metrics to Track:
1. **Conversion Rate:** Which locker converts better?
2. **Cost Per Conversion:** Which is more cost-effective?
3. **Quality Score:** Are both campaigns performing well?
4. **Revenue Per Visitor:** Track affiliate earnings by campaign

### In Google Ads:
- Check conversion data daily
- Look for statistical significance (at least 100 conversions per variant)
- Monitor cost per conversion trends

### In Google Analytics:
- Set up custom reports to track locker performance
- Monitor user behavior flow
- Track bounce rates and time on page

## 🎯 Step 5: Optimization Tips

### Week 1-2: Data Collection
- Let both campaigns run with equal budgets
- Don't make changes until you have sufficient data
- Aim for at least 50-100 conversions per variant

### Week 3+: Optimization
- **If File Locker wins:** Shift more budget to file locker campaign
- **If Captcha Locker wins:** Shift more budget to captcha locker campaign
- **If results are close:** Test other variables (headlines, images, etc.)

### Advanced Testing:
Once you know which locker works better, test:
- Different headlines
- Different button colors/text
- Different page layouts
- Different offers or bonuses

## 🔍 Troubleshooting

### Conversion Tracking Not Working?
1. Check that conversion ID and label are correct in `config.js`
2. Test form submission and check browser console for errors
3. Use Google Tag Assistant to verify tracking

### Low Conversion Rates?
1. Check mobile responsiveness
2. Test page loading speed
3. Review form fields (too many fields = lower conversions)
4. A/B test different headlines or offers

### High Cost Per Conversion?
1. Review keyword targeting (too broad?)
2. Check ad relevance and Quality Score
3. Test different ad copy
4. Adjust bidding strategy

## 📊 Sample Campaign Structure

```
Account: Make Money Online Ebooks
├── Campaign: Ebook File Locker
│   ├── Ad Group: Money Making Keywords
│   │   ├── Keywords: make money online, earn money online, etc.
│   │   └── Ads: 3-4 variations
│   └── Final URL: yourdomain.com/?locker=file&utm_campaign=ebook-file
│
└── Campaign: Ebook Captcha Locker
    ├── Ad Group: Money Making Keywords (identical to above)
    │   ├── Keywords: make money online, earn money online, etc.
    │   └── Ads: 3-4 variations (identical to above)
    └── Final URL: yourdomain.com/?locker=captcha&utm_campaign=ebook-captcha
```

## 🎉 Success Metrics

### What to Measure:
- **Primary:** Conversion rate difference between lockers
- **Secondary:** Cost per conversion, affiliate revenue per visitor
- **Long-term:** Customer lifetime value from each traffic source

### When to Make Decisions:
- **Statistical significance:** At least 95% confidence level
- **Sufficient data:** Minimum 100 conversions per variant
- **Time period:** Run test for at least 2 weeks to account for day-of-week variations

Remember: The goal is to find which locker type generates more affiliate revenue for you, not just which gets more downloads!

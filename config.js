// Configuration file for your squeeze page
// Update these settings with your actual URLs and tracking IDs

const CONFIG = {
    // Google Analytics Tracking ID
    googleAnalyticsId: 'GA_MEASUREMENT_ID', // Replace with your GA4 measurement ID
    
    // Google Ads Conversion Tracking
    googleAdsConversionId: 'AW-CONVERSION_ID', // Replace with your Google Ads conversion ID
    googleAdsConversionLabel: 'CONVERSION_LABEL', // Replace with your conversion label
    
    // Locker URLs - Replace these with your actual affiliate network URLs
    lockerUrls: {
        'file': 'https://your-affiliate-network.com/file-locker/ebook-package',
        'captcha': 'https://your-affiliate-network.com/captcha-locker/ebook-package',
        'default': 'https://your-affiliate-network.com/file-locker/ebook-package' // Fallback
    },
    
    // Email service configuration (optional)
    emailService: {
        enabled: false, // Set to true if you want to collect emails in your own system
        webhookUrl: 'https://your-webhook-url.com/collect-lead', // Your webhook endpoint
        apiKey: 'your-api-key' // If needed for your email service
    },
    
    // Split test configuration
    splitTest: {
        // Default locker type if no parameter is provided
        defaultVariant: 'file',
        
        // Available variants
        variants: ['file', 'captcha'],
        
        // Track additional parameters
        trackUtmParameters: true
    }
};

// Export for use in other files (if using modules)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}

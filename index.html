<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FREE Make Money Online Ebook Package - Download Now!</title>
    <meta name="description" content="Get your FREE Make Money Online Ebook Package! Proven strategies and methods to start earning online today.">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Google Analytics (replace GA_MEASUREMENT_ID with your actual ID) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>
    
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-4">
        <div class="container mx-auto px-4 text-center">
            <h1 class="text-2xl font-bold">💰 Make Money Online Mastery</h1>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Hero Section -->
        <div class="text-center mb-8">
            <h2 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                Get Your <span class="text-purple-600">FREE</span> Make Money Online Ebook Package!
            </h2>
            <p class="text-xl text-gray-600 mb-6">
                Discover proven strategies and step-by-step methods to start earning money online today!
            </p>
            
            <!-- Ebook Cover Image Placeholder -->
            <div class="mb-8">
                <div class="bg-gradient-to-br from-purple-500 to-blue-600 w-64 h-80 mx-auto rounded-lg shadow-2xl flex items-center justify-center text-white text-center p-6">
                    <div>
                        <div class="text-6xl mb-4">📚</div>
                        <h3 class="text-xl font-bold mb-2">Make Money Online</h3>
                        <p class="text-sm">Complete Package</p>
                        <div class="mt-4 bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                            FREE
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Benefits Section -->
        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow-lg text-center">
                <div class="text-4xl mb-4">🚀</div>
                <h3 class="text-xl font-bold mb-2">Quick Start Guide</h3>
                <p class="text-gray-600">Get started making money online in just 24 hours with our proven methods.</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg text-center">
                <div class="text-4xl mb-4">💡</div>
                <h3 class="text-xl font-bold mb-2">Multiple Strategies</h3>
                <p class="text-gray-600">Learn 10+ different ways to generate income online, from beginner to advanced.</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-lg text-center">
                <div class="text-4xl mb-4">📈</div>
                <h3 class="text-xl font-bold mb-2">Scale Your Income</h3>
                <p class="text-gray-600">Step-by-step instructions to scale from $100 to $1000+ per month.</p>
            </div>
        </div>

        <!-- Lead Capture Form -->
        <div class="bg-white rounded-lg shadow-2xl p-8 max-w-md mx-auto">
            <h3 class="text-2xl font-bold text-center mb-6 text-gray-800">
                Download Your FREE Ebook Package Now!
            </h3>
            
            <form id="leadForm" class="space-y-4">
                <div>
                    <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                    <input type="text" id="firstName" name="firstName" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                           placeholder="Enter your first name">
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <input type="email" id="email" name="email" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                           placeholder="Enter your email address">
                </div>
                
                <button type="submit" id="downloadBtn" 
                        class="w-full bg-gradient-to-r from-purple-600 to-blue-600 text-white font-bold py-4 px-6 rounded-lg hover:from-purple-700 hover:to-blue-700 transform transition duration-200 pulse-animation">
                    🔥 GET MY FREE EBOOK PACKAGE NOW! 🔥
                </button>
            </form>
            
            <p class="text-xs text-gray-500 text-center mt-4">
                We respect your privacy. Your information will never be shared.
            </p>
        </div>

        <!-- Social Proof -->
        <div class="text-center mt-8">
            <p class="text-gray-600 mb-4">Join 10,000+ people who have already downloaded this package!</p>
            <div class="flex justify-center space-x-2">
                <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                <span class="text-gray-600">"This ebook changed my life!" - Sarah M.</span>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-6 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 Make Money Online Mastery. All rights reserved.</p>
        </div>
    </footer>

    <!-- JavaScript for URL Parameter Detection and Form Handling -->
    <script>
        // Get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Detect locker type from URL parameter
        const lockerType = getUrlParameter('locker') || 'file'; // Default to file locker
        
        // Track which variant is being shown
        console.log('Locker variant:', lockerType);
        
        // Send tracking event to Google Analytics
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_view', {
                'custom_parameter': 'locker_type',
                'value': lockerType
            });
        }

        // Form submission handler
        document.getElementById('leadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const firstName = document.getElementById('firstName').value;
            const email = document.getElementById('email').value;
            
            if (!firstName || !email) {
                alert('Please fill in all fields.');
                return;
            }
            
            // Track conversion
            if (typeof gtag !== 'undefined') {
                gtag('event', 'conversion', {
                    'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL', // Replace with your conversion tracking
                    'value': 1.0,
                    'currency': 'USD',
                    'custom_parameter': lockerType
                });
            }
            
            // Store lead data (you can send this to your backend/email service)
            const leadData = {
                firstName: firstName,
                email: email,
                lockerType: lockerType,
                timestamp: new Date().toISOString(),
                utmSource: getUrlParameter('utm_source'),
                utmMedium: getUrlParameter('utm_medium'),
                utmCampaign: getUrlParameter('utm_campaign')
            };
            
            console.log('Lead captured:', leadData);
            
            // Redirect based on locker type
            redirectToLocker(lockerType, leadData);
        });

        function redirectToLocker(type, leadData) {
            // Replace these URLs with your actual locker URLs
            const lockerUrls = {
                'file': 'https://your-affiliate-network.com/file-locker/ebook-package',
                'captcha': 'https://your-affiliate-network.com/captcha-locker/ebook-package'
            };
            
            const targetUrl = lockerUrls[type] || lockerUrls['file'];
            
            // You can append lead data to the URL if needed
            const urlWithData = `${targetUrl}?email=${encodeURIComponent(leadData.email)}&name=${encodeURIComponent(leadData.firstName)}`;
            
            // Redirect to the appropriate locker
            window.location.href = urlWithData;
        }
    </script>
</body>
</html>

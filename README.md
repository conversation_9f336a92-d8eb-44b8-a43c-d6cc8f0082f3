# Make Money Online Ebook Squeeze Page

A simple, effective squeeze page for collecting leads and split testing different affiliate locker types.

## 🚀 Quick Setup

1. **Upload files to your web hosting**
   - Upload `index.html` to your domain root or subdirectory
   - Upload `config.js` to the same location

2. **Configure your settings**
   - Edit `config.js` with your actual URLs and tracking IDs
   - Replace placeholder URLs with your affiliate network locker URLs

3. **Set up Google Analytics & Ads tracking**
   - Replace `GA_MEASUREMENT_ID` with your Google Analytics 4 measurement ID
   - Replace `AW-CONVERSION_ID` and `CONVERSION_LABEL` with your Google Ads conversion tracking

## 📊 Split Testing with Google Ads

### Method 1: URL Parameters (Recommended)
Create different campaigns in Google Ads with these final URLs:

**Campaign A (File Locker):**
```
https://yourdomain.com/?locker=file&utm_source=google&utm_medium=cpc&utm_campaign=ebook-file
```

**Campaign B (Captcha Locker):**
```
https://yourdomain.com/?locker=captcha&utm_source=google&utm_medium=cpc&utm_campaign=ebook-captcha
```

### Method 2: Separate Landing Pages
1. Copy `index.html` to `file-locker.html` and `captcha-locker.html`
2. Edit each file to hardcode the locker type
3. Use different URLs in your Google Ads campaigns

## 🎯 Tracking & Analytics

The page automatically tracks:
- Page views by locker type
- Form submissions (conversions)
- UTM parameters from your campaigns
- Lead data (name, email, timestamp)

### Google Ads Conversion Tracking
1. Set up conversion tracking in Google Ads
2. Update the conversion ID and label in `config.js`
3. The page will automatically fire conversion events when forms are submitted

### Google Analytics Events
- `page_view` with locker type
- `conversion` with locker type and value

## 🔧 Customization

### Change Locker URLs
Edit the `lockerUrls` object in `config.js`:
```javascript
lockerUrls: {
    'file': 'https://your-actual-file-locker-url.com',
    'captcha': 'https://your-actual-captcha-locker-url.com'
}
```

### Add More Variants
1. Add new locker types to `config.js`
2. Use URL parameter like `?locker=newtype`
3. The page will automatically handle the new variant

### Styling
The page uses Tailwind CSS loaded from CDN. You can:
- Modify the existing classes in `index.html`
- Add custom CSS in the `<style>` section
- Replace with your own CSS framework

## 📱 Features

- ✅ Fully responsive design
- ✅ Mobile-optimized
- ✅ Fast loading (CDN-based CSS)
- ✅ SEO-friendly
- ✅ Conversion-optimized copy
- ✅ Social proof elements
- ✅ Professional design
- ✅ Easy split testing
- ✅ Analytics tracking

## 🔍 Testing Your Setup

1. **Test File Locker:**
   Visit: `https://yourdomain.com/?locker=file`

2. **Test Captcha Locker:**
   Visit: `https://yourdomain.com/?locker=captcha`

3. **Check Console:**
   Open browser developer tools to see tracking events

4. **Verify Redirects:**
   Submit the form to ensure proper redirection to your lockers

## 📈 Optimization Tips

1. **A/B Test Headlines:** Try different headlines to improve conversion rates
2. **Test Button Colors:** Change the download button color/text
3. **Add Urgency:** Include countdown timers or limited-time offers
4. **Social Proof:** Add more testimonials or download counters
5. **Mobile First:** Ensure the page looks great on mobile devices

## 🛠️ Advanced Features (Optional)

### Email Collection
If you want to collect emails in your own system before redirecting:
1. Set `emailService.enabled = true` in `config.js`
2. Set up a webhook endpoint to receive lead data
3. The page will POST lead data to your webhook before redirecting

### Custom Tracking
Add additional tracking by modifying the JavaScript in `index.html`:
```javascript
// Track custom events
gtag('event', 'custom_event', {
    'event_category': 'engagement',
    'event_label': 'button_click'
});
```

## 📞 Support

If you need help setting this up or want customizations, feel free to ask!
